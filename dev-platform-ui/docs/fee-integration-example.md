# 费用计算集成示例

## 后端集成完成的功能

### 1. 控制器方法更新

#### `saveOrUpdate` 方法
- ✅ 在保存钱币明细前自动计算所有钱币的费用
- ✅ 计算送评单总费用并更新到主表
- ✅ 记录详细的费用计算日志

```java
// 在保存前计算所有钱币的费用
log.info("开始计算钱币费用，共{}个钱币", allItems.size());
FeeCalculatorUtil.batchUpdateGradeFee(allItems);

// 计算送评单总费用并更新
FeeCalculatorUtil.FeeDetail feeDetail = FeeCalculatorUtil.calculateSendformTotalFee(sendform, allItems);
sendform.setSumFinalFee(feeDetail.getTotalFee());
sendform.setSumBoxFee(feeDetail.getBoxFeeTotal());
```

#### `addCoins` 方法
- ✅ 添加钱币时自动计算新钱币的费用
- ✅ 重新计算整个送评单的总费用
- ✅ 更新送评单费用信息

#### `updateCoin` 方法
- ✅ 更新单个钱币时重新计算费用
- ✅ 如果钱币属于送评单，重新计算送评单总费用
- ✅ 完整的错误处理和日志记录

#### 新增方法
- ✅ `batchUpdateCoins` - 批量更新钱币信息
- ✅ `recalculateFee` - 重新计算单个送评单费用
- ✅ `batchRecalculateFee` - 批量重新计算多个送评单费用
- ✅ `removeCoin` - 删除钱币时重新计算费用

### 2. 费用计算工具类

#### `FeeCalculatorUtil.java`
- ✅ 完整的费用计算逻辑
- ✅ 与旧系统保持一致的计算规则
- ✅ 支持批量计算和验证
- ✅ 详细的费用明细返回

## 前端集成示例

### 1. 在组件中使用费用计算

```vue
<template>
  <div class="sendform-edit">
    <!-- 送评单基本信息 -->
    <el-form :model="form" ref="formRef">
      <!-- ... 其他字段 ... -->
      
      <!-- 费用信息显示 -->
      <el-row>
        <el-col :span="6">
          <el-form-item label="评级费总计">
            <el-input :value="formatFee(feeDetail.gradeFeeTotal)" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="盒子费总计">
            <el-input :value="formatFee(feeDetail.boxFeeTotal)" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="加急费总计">
            <el-input :value="formatFee(feeDetail.urgentFeeTotal)" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="总费用">
            <el-input :value="formatFee(feeDetail.totalFee)" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 钱币列表 -->
    <CoinDetailTable 
      :data="coinList"
      @field-change="onCoinFieldChange"
    />

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="recalculateAllFees">重新计算费用</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useFeeCalculator } from '@/composables/use-fee-calculator';
import { recalculateFee } from '../api';
import CoinDetailTable from './CoinDetailTable.vue';

const form = ref({
  sendform: {},
  coinList: []
});

const {
  feeDetail,
  handleFeeFieldChange,
  calculateTotalFee,
  formatFee
} = useFeeCalculator();

// 处理钱币字段变化
const onCoinFieldChange = ({ row, field }) => {
  handleFeeFieldChange(row, field);
  // 重新计算总费用
  calculateTotalFee(form.value.sendform, form.value.coinList);
};

// 重新计算所有费用
const recalculateAllFees = async () => {
  if (form.value.sendform.sendnum) {
    try {
      const result = await recalculateFee(form.value.sendform.sendnum);
      feeDetail.value = result.feeDetail;
      ElMessage.success('费用重新计算完成');
    } catch (error) {
      ElMessage.error('费用计算失败：' + error.message);
    }
  }
};

// 提交表单
const onSubmit = async () => {
  // 表单验证和提交逻辑
  // 费用会在后端自动计算，无需前端处理
};
</script>
```

### 2. 使用新的API接口

```javascript
import { 
  batchUpdateCoins, 
  recalculateFee, 
  batchRecalculateFee 
} from '../api';

// 批量更新钱币信息
const updateCoinsWithFee = async (coins) => {
  try {
    await batchUpdateCoins(coins);
    ElMessage.success('钱币信息更新成功，费用已重新计算');
  } catch (error) {
    ElMessage.error('更新失败：' + error.message);
  }
};

// 重新计算单个送评单费用
const recalculateSingleFee = async (sendnum) => {
  try {
    const result = await recalculateFee(sendnum);
    console.log('费用计算结果：', result);
    return result;
  } catch (error) {
    console.error('费用计算失败：', error);
    throw error;
  }
};

// 批量重新计算多个送评单费用
const recalculateBatchFees = async (sendnums) => {
  try {
    const result = await batchRecalculateFee(sendnums);
    console.log('批量费用计算结果：', result);
    return result;
  } catch (error) {
    console.error('批量费用计算失败：', error);
    throw error;
  }
};
```

### 3. 在钱币表格中集成费用计算

```vue
<!-- CoinDetailTable.vue 中的费用相关列 -->
<el-table-column prop="standardPrice" label="标准价" width="100">
  <template #default="{ row }">
    <el-input-number
      v-model="row.standardPrice"
      :min="0"
      :precision="2"
      size="small"
      @change="handleFeeFieldChange(row, 'standardPrice')"
    />
  </template>
</el-table-column>

<el-table-column prop="internationalPrice" label="国际价" width="100">
  <template #default="{ row }">
    <el-input-number
      v-model="row.internationalPrice"
      :min="0"
      :precision="2"
      size="small"
      @change="handleFeeFieldChange(row, 'internationalPrice')"
    />
  </template>
</el-table-column>

<el-table-column prop="discount" label="折扣" width="80">
  <template #default="{ row }">
    <el-input-number
      v-model="row.discount"
      :min="0"
      :max="20"
      :precision="1"
      size="small"
      @change="handleFeeFieldChange(row, 'discount')"
    />
  </template>
</el-table-column>

<el-table-column prop="gradeFee" label="评级费" width="100">
  <template #default="{ row }">
    <el-input-number
      v-model="row.gradeFee"
      :min="0"
      :precision="2"
      size="small"
      disabled
      style="background-color: #f5f7fa;"
    />
  </template>
</el-table-column>
```

## 使用流程

### 1. 创建/编辑送评单
1. 用户填写送评单基本信息
2. 添加钱币信息（标准价、国际价、折扣等）
3. 系统自动计算每个钱币的评级费
4. 提交时后端自动计算总费用并保存

### 2. 添加钱币
1. 调用 `addCoins` API
2. 后端自动计算新钱币的费用
3. 重新计算整个送评单的总费用
4. 更新送评单费用信息

### 3. 更新钱币信息
1. 调用 `updateCoin` 或 `batchUpdateCoins` API
2. 后端重新计算钱币费用
3. 如果钱币属于送评单，重新计算送评单总费用

### 4. 手动重新计算费用
1. 调用 `recalculateFee` API
2. 后端重新计算所有钱币费用
3. 更新送评单总费用
4. 返回详细的费用明细

## 注意事项

1. **自动计算**：所有涉及钱币的操作都会自动触发费用计算
2. **费用优先级**：国际价 > 标准价
3. **折扣处理**：折扣为0时费用直接为0
4. **错误处理**：完整的异常处理和日志记录
5. **性能优化**：批量操作使用批量计算方法
6. **数据一致性**：事务保证数据的一致性

## 测试建议

1. 测试费用计算的准确性
2. 测试批量操作的性能
3. 测试异常情况的处理
4. 测试前后端数据的一致性
5. 测试日志记录的完整性
